<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>命运轨迹 - 基于八字的人生模拟</title>
    <!-- Markdown解析库 -->
    <script src="https://cdn.jsdelivr.net/npm/marked@9.1.6/marked.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(45deg, #FF6B6B, #4ECDC4);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .header p {
            font-size: 1.1em;
            opacity: 0.9;
        }
        
        .main-content {
            display: flex;
            min-height: 600px;
        }
        
        .sidebar {
            width: 320px;
            background: #f8f9fa;
            padding: 20px;
            border-right: 2px solid #dee2e6;
            overflow-y: auto;
            max-height: 100vh;
        }
        
        .bazi-info {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 12px;
            margin-bottom: 20px;
        }
        
        .bazi-info h3 {
            margin-bottom: 15px;
            font-size: 1.2em;
        }
        
        .info-item {
            margin: 8px 0;
            padding: 5px 0;
            border-bottom: 1px solid rgba(255,255,255,0.2);
        }
        
        .life-stage {
            background: white;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 15px;
        }
        
        .life-stage.current {
            border-color: #FF6B6B;
            background: linear-gradient(135deg, #FF6B6B 0%, #4ECDC4 100%);
            color: white;
        }
        
        .life-stage h4 {
            margin-bottom: 8px;
            font-size: 1.1em;
        }
        
        .life-stage p {
            font-size: 0.9em;
            opacity: 0.8;
        }
        
        .life-stage:hover {
            background: #f0f0f0;
            cursor: pointer;
            transform: translateX(5px);
            transition: all 0.3s ease;
        }
        
        .life-stage.current:hover {
            transform: translateX(5px);
        }
        
        .ai-config-panel {
            background: linear-gradient(135deg, #fd79a8 0%, #e84393 100%);
            color: white;
            padding: 20px;
            border-radius: 12px;
            margin-top: 20px;
        }
        
        .ai-config-panel h4 {
            margin-bottom: 15px;
            text-align: center;
        }
        
        .config-status {
            text-align: center;
            margin-bottom: 15px;
            font-size: 1.1em;
        }
        
        .stats-panel {
            background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
            color: white;
            padding: 20px;
            border-radius: 12px;
            margin-top: 20px;
        }
        
        .stats-panel h4 {
            margin-bottom: 15px;
            text-align: center;
        }
        
        .stat-item {
            display: flex;
            justify-content: space-between;
            margin: 8px 0;
            padding: 5px 0;
            border-bottom: 1px solid rgba(255,255,255,0.2);
        }
        
        .control-buttons {
            display: flex;
            gap: 10px;
            margin-top: 20px;
        }
        
        .control-btn {
            flex: 1;
            background: #6c5ce7;
            color: white;
            border: none;
            padding: 12px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 1em;
            transition: background 0.3s ease;
        }
        
        .control-btn:hover {
            background: #5a4fcf;
        }
        
        .control-btn:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        
        .game-area {
            flex: 1;
            padding: 30px;
        }
        
        .game-settings {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            color: white;
            padding: 40px;
            border-radius: 15px;
            text-align: center;
        }
        
        .settings-title {
            font-size: 2em;
            margin-bottom: 30px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .settings-content {
            background: rgba(255,255,255,0.1);
            padding: 30px;
            border-radius: 12px;
            margin-bottom: 30px;
        }
        
        .setting-group {
            margin-bottom: 30px;
        }
        
        .setting-group h4 {
            margin-bottom: 20px;
            font-size: 1.4em;
        }
        
        .style-options {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
        }
        
        .style-option {
            cursor: pointer;
        }
        
        .style-option input[type="radio"] {
            display: none;
        }
        
        .option-card {
            background: rgba(255,255,255,0.1);
            padding: 20px;
            border-radius: 12px;
            border: 2px solid transparent;
            transition: all 0.3s ease;
        }
        
        .style-option input[type="radio"]:checked + .option-card {
            border-color: #ffd700;
            background: rgba(255,215,0,0.2);
        }
        
        .option-card:hover {
            background: rgba(255,255,255,0.2);
            transform: translateY(-2px);
        }
        
        .option-icon {
            font-size: 2em;
            margin-bottom: 10px;
        }
        
        .option-title {
            font-size: 1.2em;
            font-weight: bold;
            margin-bottom: 8px;
        }
        
        .option-desc {
            font-size: 0.9em;
            opacity: 0.9;
        }
        
        .time-options {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
        }
        
        .time-option {
            cursor: pointer;
        }
        
        .time-option input[type="radio"] {
            display: none;
        }
        
        .time-card {
            background: rgba(255,255,255,0.1);
            padding: 20px;
            border-radius: 12px;
            border: 2px solid transparent;
            transition: all 0.3s ease;
            text-align: center;
        }
        
        .time-option input[type="radio"]:checked + .time-card {
            border-color: #ffd700;
            background: rgba(255,215,0,0.2);
        }
        
        .time-card:hover {
            background: rgba(255,255,255,0.2);
            transform: translateY(-2px);
        }
        
        .time-icon {
            font-size: 2em;
            margin-bottom: 10px;
        }
        
        .time-title {
            font-size: 1.1em;
            font-weight: bold;
            margin-bottom: 8px;
        }
        
        .time-desc {
            font-size: 0.9em;
            opacity: 0.9;
        }
        
        .setting-actions {
            text-align: center;
        }
        
        .start-game-btn {
            background: linear-gradient(45deg, #4ECDC4, #44A08D);
            color: white;
            border: none;
            padding: 15px 40px;
            border-radius: 25px;
            font-size: 1.2em;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
        }
        
        .start-game-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0,0,0,0.3);
        }
        
        .story-section {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            color: white;
            padding: 25px;
            border-radius: 12px;
            margin-bottom: 25px;
            box-shadow: 0 8px 32px rgba(240, 147, 251, 0.3);
        }
        
        .story-title {
            font-size: 1.5em;
            margin-bottom: 15px;
            text-align: center;
        }
        
        .story-content {
            background: rgba(255,255,255,0.1);
            padding: 20px;
            border-radius: 8px;
            line-height: 1.8;
            margin-bottom: 20px;
        }
        
        .story-content h1, .story-content h2, .story-content h3 {
            color: #fff;
            margin: 15px 0 10px 0;
        }
        
        .story-content p {
            margin: 10px 0;
        }
        
        .story-content strong {
            color: #ffd700;
        }
        
        .story-content em {
            color: #ffc0cb;
        }
        
        .choices-section {
            background: white;
            padding: 25px;
            border-radius: 12px;
            box-shadow: 0 4px 16px rgba(0,0,0,0.1);
        }
        
        .choices-title {
            font-size: 1.3em;
            color: #333;
            margin-bottom: 20px;
            text-align: center;
            font-weight: bold;
        }
        
        .choice-button {
            width: 100%;
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 15px 20px;
            margin: 10px 0;
            border-radius: 8px;
            cursor: pointer;
            font-size: 1em;
            text-align: left;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            position: relative;
        }
        
        .choice-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(102, 126, 234, 0.3);
        }
        
        .choice-button:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }
        
        .choice-number {
            display: inline-block;
            background: rgba(255,255,255,0.2);
            border-radius: 50%;
            width: 25px;
            height: 25px;
            text-align: center;
            line-height: 25px;
            margin-right: 10px;
            font-weight: bold;
        }
        
        .loading {
            text-align: center;
            padding: 40px;
            color: #6c5ce7;
            font-size: 1.2em;
        }
        
        .loading-spinner {
            display: inline-block;
            width: 30px;
            height: 30px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #6c5ce7;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-right: 10px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        @keyframes blink {
            0%, 50% { opacity: 1; }
            51%, 100% { opacity: 0; }
        }
        
        .history-panel {
            background: linear-gradient(135deg, #a29bfe 0%, #6c5ce7 100%);
            color: white;
            padding: 20px;
            border-radius: 12px;
            margin-top: 20px;
            max-height: 300px;
            overflow-y: auto;
        }
        
        .history-panel h4 {
            margin-bottom: 15px;
            text-align: center;
            border-bottom: 2px solid rgba(255,255,255,0.3);
            padding-bottom: 10px;
        }
        
        .history-item {
            background: rgba(255,255,255,0.1);
            padding: 12px;
            margin: 10px 0;
            border-radius: 8px;
            border-left: 4px solid rgba(255,255,255,0.5);
        }
        
        .history-item-title {
            font-weight: bold;
            margin-bottom: 5px;
            font-size: 0.9em;
        }
        
        .history-item-choice {
            font-size: 0.85em;
            opacity: 0.9;
            background: rgba(255,255,255,0.1);
            padding: 5px 8px;
            border-radius: 4px;
            margin-top: 5px;
        }
        
        .history-item-age {
            font-size: 0.8em;
            opacity: 0.7;
            text-align: right;
            margin-top: 5px;
        }
        
        .empty-history {
            text-align: center;
            opacity: 0.7;
            font-style: italic;
            padding: 20px 0;
        }
        
        /* 右侧浮动人生轨迹面板 */
        .trajectory-panel {
            position: fixed;
            top: 100px;
            right: 20px;
            width: 280px;
            max-height: calc(100vh - 140px);
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            z-index: 1000;
            overflow-y: auto;
            padding: 20px;
            backdrop-filter: blur(10px);
        }
        
        /* 模态框样式 */
        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.8);
            z-index: 2000;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }
        
        .modal-content {
            background: white;
            border-radius: 15px;
            max-width: 500px;
            width: 100%;
            max-height: 80vh;
            overflow-y: auto;
            position: relative;
        }
        
        .modal-header {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            padding: 20px 30px;
            border-radius: 15px 15px 0 0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .modal-header h3 {
            margin: 0;
            font-size: 1.3em;
        }
        
        .close-btn {
            background: none;
            border: none;
            color: white;
            font-size: 1.5em;
            cursor: pointer;
            padding: 0;
            width: 30px;
            height: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            transition: background 0.3s ease;
        }
        
        .close-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        
        .modal-body {
            padding: 30px;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: bold;
            color: #333;
        }
        
        .form-group input,
        .form-group select,
        .form-group textarea {
            width: 100%;
            padding: 12px;
            border: 2px solid #e1e8ed;
            border-radius: 8px;
            font-size: 1em;
            transition: border-color 0.3s ease;
            box-sizing: border-box;
        }
        
        .form-group input:focus, .form-group select:focus, .form-group textarea:focus {
            outline: none;
            border-color: #4ECDC4;
            box-shadow: 0 0 0 3px rgba(78, 205, 196, 0.1);
        }
        
        .modal-footer {
            padding: 20px 30px;
            border-top: 1px solid #eee;
            display: flex;
            gap: 15px;
            justify-content: flex-end;
        }
        
        .modal-btn {
            padding: 12px 25px;
            border: none;
            border-radius: 8px;
            font-size: 1em;
            cursor: pointer;
            transition: background 0.3s ease;
        }
        
        .modal-btn.cancel {
            background: #6c757d;
            color: white;
        }
        
        .modal-btn.cancel:hover {
            background: #5a6268;
        }
        
        .modal-btn.save {
            background: linear-gradient(45deg, #FF6B6B, #4ECDC4);
            color: white;
        }
        
        .modal-btn.save:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
        }
        
        @media (max-width: 768px) {
            .main-content {
                flex-direction: column;
            }
            
            .sidebar {
                width: 100%;
                border-right: none;
                border-bottom: 2px solid #dee2e6;
            }
            
            .trajectory-panel {
                position: relative;
                right: auto;
                top: auto;
                width: 100%;
                max-height: 300px;
                margin-top: 20px;
            }
            
            .container {
                margin: 10px;
                border-radius: 10px;
            }
            
            .header h1 {
                font-size: 2em;
            }
            
            .modal-content {
                width: 95%;
                margin: 20px;
            }
            
            .modal-body {
                padding: 20px;
            }
            
            .modal-footer {
                flex-direction: column;
            }
            
            .modal-btn {
                width: 100%;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎭 命运轨迹</h1>
            <p>基于八字命理的人生模拟游戏</p>
        </div>
        
        <div class="main-content">
            <div class="sidebar">
                <div class="bazi-info" id="baziInfo">
                    <h3>📊 命盘信息</h3>
                    <div class="info-item">正在加载...</div>
                </div>
                
                <div id="lifeStages">
                    <!-- 人生阶段会动态生成 -->
                </div>
                
                <div class="ai-config-panel" id="aiConfigPanel">
                    <h4>🤖 AI配置</h4>
                    <div class="config-status" id="configStatus">
                        <span style="color: #ff6b6b;">未配置</span>
                    </div>
                    <button class="control-btn" onclick="showAIConfig()" id="configBtn">⚙️ 配置AI</button>
                </div>
                
                <div class="stats-panel">
                    <h4>📈 人生数据</h4>
                    <div class="stat-item">
                        <span>年龄</span>
                        <span id="currentAge">0岁</span>
                    </div>
                    <div class="stat-item">
                        <span>命运值</span>
                        <span id="destinyScore">100</span>
                    </div>
                    <div class="stat-item">
                        <span>事件计数</span>
                        <span id="eventCount">0</span>
                    </div>
                </div>
                
                <div class="control-buttons">
                    <button class="control-btn" onclick="resetGame()">🔄 重新开始</button>
                    <button class="control-btn" onclick="window.close()">🏠 返回主页</button>
                </div>
            </div>
            
            <div class="game-area">
                <!-- 游戏设置界面 -->
                <div class="game-settings" id="gameSettings">
                    <div class="settings-title">🎭 命运轨迹设置</div>
                    <div class="settings-content">
                        <div class="setting-group">
                            <h4>📖 故事风格</h4>
                            <div class="style-options">
                                <label class="style-option">
                                    <input type="radio" name="storyStyle" value="realistic" checked>
                                    <div class="option-card">
                                        <div class="option-icon">🌍</div>
                                        <div class="option-title">现实写实</div>
                                        <div class="option-desc">基于真实历史事件和社会背景，模拟真实人生轨迹</div>
                                    </div>
                                </label>
                                
                                <label class="style-option">
                                    <input type="radio" name="storyStyle" value="fantasy">
                                    <div class="option-card">
                                        <div class="option-icon">🔮</div>
                                        <div class="option-title">奇幻冒险</div>
                                        <div class="option-desc">融入奇幻元素，命理法则化为魔法力量的冒险世界</div>
                                    </div>
                                </label>
                                
                                <label class="style-option">
                                    <input type="radio" name="storyStyle" value="historical">
                                    <div class="option-card">
                                        <div class="option-icon">🏛️</div>
                                        <div class="option-title">古代传奇</div>
                                        <div class="option-desc">穿越古代，体验传统命理在历史背景下的人生</div>
                                    </div>
                                </label>
                                
                                <label class="style-option">
                                    <input type="radio" name="storyStyle" value="modern">
                                    <div class="option-card">
                                        <div class="option-icon">🏙️</div>
                                        <div class="option-title">都市传说</div>
                                        <div class="option-desc">现代都市背景，命理与科技的奇妙融合</div>
                                    </div>
                                </label>
                            </div>
                        </div>
                        
                        <div class="setting-group" id="realisticOptions">
                            <h4>⏰ 开始时间</h4>
                            <div class="time-options">
                                <label class="time-option">
                                    <input type="radio" name="startTime" value="birth" checked>
                                    <div class="time-card">
                                        <div class="time-icon">👶</div>
                                        <div class="time-title">从出生开始</div>
                                        <div class="time-desc">完整体验人生轨迹，见证历史变迁</div>
                                    </div>
                                </label>
                                
                                <label class="time-option">
                                    <input type="radio" name="startTime" value="current">
                                    <div class="time-card">
                                        <div class="time-icon">📅</div>
                                        <div class="time-title">从现在开始</div>
                                        <div class="time-desc">基于当前年龄，展望未来人生</div>
                                    </div>
                                </label>
                            </div>
                        </div>
                        
                        <div class="setting-actions">
                            <button class="start-game-btn" onclick="startGameWithSettings()">
                                🚀 开始命运之旅
                            </button>
                        </div>
                    </div>
                </div>
                
                <!-- 游戏主界面 -->
                <div class="story-section" id="storySection" style="display: none;">
                    <div class="story-title" id="storyTitle">命运的开始</div>
                    <div class="story-content" id="storyContent">
                        <div class="loading">
                            <div class="loading-spinner"></div>
                            正在根据您的八字排盘生成专属剧情...
                        </div>
                    </div>
                </div>
                
                <div class="choices-section" id="choicesSection" style="display: none;">
                    <div class="choices-title">🤔 您会如何选择？</div>
                    <div id="choicesList">
                        <!-- 选择按钮会动态生成 -->
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 右侧浮动人生轨迹面板 -->
        <div class="trajectory-panel" id="trajectoryPanel">
            <div class="history-panel" id="historyPanel">
                <h4>📜 人生轨迹</h4>
                <div id="historyContent">
                    <div class="empty-history">暂无历史记录</div>
                </div>
            </div>
        </div>
        
        <!-- AI配置模态框 -->
        <div id="aiConfigModal" class="modal" style="display: none;">
            <div class="modal-content">
                <div class="modal-header">
                    <h3>🤖 AI配置设置</h3>
                    <button class="close-btn" onclick="hideAIConfig()">&times;</button>
                </div>
                <div class="modal-body">
                    <div class="form-group">
                        <label for="modalAiProvider">AI服务商</label>
                        <select id="modalAiProvider" onchange="updateModalAIConfig()">
                            <option value="openai">OpenAI (GPT)</option>
                            <option value="claude">Anthropic (Claude)</option>
                            <option value="deepseek">DeepSeek</option>
                            <option value="custom">自定义服务商</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="modalAiModel">AI模型</label>
                        <select id="modalAiModel">
                            <option value="gpt-3.5-turbo">GPT-3.5 Turbo</option>
                            <option value="gpt-4">GPT-4</option>
                            <option value="gpt-4-turbo">GPT-4 Turbo</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="modalAiApiKey">API Key</label>
                        <input type="password" id="modalAiApiKey" placeholder="请输入您的API Key">
                    </div>
                    <div class="form-group">
                        <label for="modalAiApiUrl">API地址 (可选)</label>
                        <input type="text" id="modalAiApiUrl" placeholder="留空使用默认地址">
                    </div>
                    <div class="form-group" id="modalCustomModelGroup" style="display: none;">
                        <label for="modalCustomModel">自定义模型名</label>
                        <input type="text" id="modalCustomModel" placeholder="输入自定义模型名">
                    </div>
                    <div class="form-group" id="modalCustomHeaderGroup" style="display: none;">
                        <label for="modalCustomHeaders">自定义Headers (JSON格式)</label>
                        <textarea id="modalCustomHeaders" placeholder='{"X-Custom-Header": "value"}' rows="3"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button class="modal-btn cancel" onclick="hideAIConfig()">取消</button>
                    <button class="modal-btn save" onclick="saveAIConfig()">保存配置</button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 游戏状态
        let gameState = {
            baziData: null,
            currentAge: 0,
            currentStage: '童年',
            destinyScore: 100,
            eventCount: 0,
            choiceHistory: [],
            storyHistory: [],
            settings: {
                storyStyle: 'realistic',
                startTime: 'birth'
            }
        };
        
        // AI配置
        let aiConfig = {
            provider: 'openai',
            model: 'gpt-3.5-turbo',
            api_key: '',
            api_url: ''
        };
        
        // 人生阶段配置
        const lifeStages = [
            { name: '童年', ageRange: '0-8岁', description: '天真烂漫的启蒙时期' },
            { name: '少年', ageRange: '9-18岁', description: '求学成长的关键时期' },
            { name: '青年', ageRange: '19-28岁', description: '事业起步的奋斗时期' },
            { name: '壮年', ageRange: '29-38岁', description: '事业家庭的双线发展' },
            { name: '中年', ageRange: '39-48岁', description: '人生巅峰的收获时期' },
            { name: '中老年', ageRange: '49-58岁', description: '智慧沉淀的传承时期' },
            { name: '老年', ageRange: '59-68岁', description: '回首人生的总结时期' }
        ];
        
        // 初始化游戏
        async function initGame() {
            try {
                // 从localStorage获取八字数据
                const baziDataStr = localStorage.getItem('baziData');
                if (!baziDataStr) {
                    alert('未找到八字数据，请先完成八字分析');
                    window.close();
                    return;
                }
                
                gameState.baziData = JSON.parse(baziDataStr);
                
                // 显示八字信息
                displayBaziInfo();
                
                // 生成人生阶段
                generateLifeStages();
                
                // 获取AI配置
                loadAIConfig();
                
                // 设置故事风格选择事件
                setupStyleSelectionEvents();
                
            } catch (error) {
                console.error('初始化游戏失败:', error);
                alert('游戏初始化失败，请重试');
            }
        }
        
        // 设置故事风格选择事件
        function setupStyleSelectionEvents() {
            const storyStyleRadios = document.querySelectorAll('input[name="storyStyle"]');
            const realisticOptions = document.getElementById('realisticOptions');
            
            storyStyleRadios.forEach(radio => {
                radio.addEventListener('change', function() {
                    if (this.value === 'realistic') {
                        realisticOptions.style.display = 'block';
                    } else {
                        realisticOptions.style.display = 'none';
                    }
                });
            });
        }
        
        // 根据设置开始游戏
        async function startGameWithSettings() {
            try {
                // 获取用户选择的设置
                const storyStyle = document.querySelector('input[name="storyStyle"]:checked').value;
                const startTime = document.querySelector('input[name="startTime"]:checked')?.value || 'birth';
                
                // 保存设置到游戏状态
                gameState.settings.storyStyle = storyStyle;
                gameState.settings.startTime = startTime;
                
                // 根据开始时间设置初始年龄
                if (storyStyle === 'realistic' && startTime === 'current') {
                    // 计算当前年龄（简化计算，假设生日已过）
                    const birthYear = parseInt(gameState.baziData.birth_info.date.split('-')[0]);
                    const currentYear = new Date().getFullYear();
                    gameState.currentAge = currentYear - birthYear;
                } else {
                    gameState.currentAge = 0;
                }
                
                // 更新游戏统计显示
                updateGameStats();
                updateCurrentStage();
                
                // 隐藏设置界面，显示游戏界面
                document.getElementById('gameSettings').style.display = 'none';
                document.getElementById('storySection').style.display = 'block';
                
                // 开始第一个故事
                await generateNextStory();
                
            } catch (error) {
                console.error('开始游戏失败:', error);
                alert('开始游戏失败，请重试');
            }
        }
        
        // 显示八字信息
        function displayBaziInfo() {
            const baziInfo = document.getElementById('baziInfo');
            const birthInfo = gameState.baziData.birth_info;
            
            baziInfo.innerHTML = `
                <h3>📊 命盘信息</h3>
                <div class="info-item">📅 ${birthInfo.date}</div>
                <div class="info-item">⏰ ${birthInfo.time}点</div>
                <div class="info-item">👤 ${birthInfo.gender}</div>
                <div class="info-item">🐲 ${birthInfo.shengxiao}</div>
            `;
        }
        
        // 生成人生阶段
        function generateLifeStages() {
            const stagesContainer = document.getElementById('lifeStages');
            stagesContainer.innerHTML = lifeStages.map((stage, index) => `
                <div class="life-stage ${index === 0 ? 'current' : ''}" id="stage-${index}" onclick="viewStageHistory(${index})">
                    <h4>${stage.name}</h4>
                    <p>${stage.ageRange}</p>
                    <p>${stage.description}</p>
                    <div class="stage-event-count" id="stageCount-${index}"></div>
                </div>
            `).join('');
            
            // 更新阶段事件计数
            updateStageEventCounts();
        }
        
        // 更新阶段事件计数
        function updateStageEventCounts() {
            lifeStages.forEach((stage, index) => {
                const minAge = index * 10;
                const maxAge = (index + 1) * 10 - 1;
                const eventsInStage = gameState.storyHistory.filter(story => 
                    story.age >= minAge && story.age <= maxAge
                ).length;
                
                const countElement = document.getElementById(`stageCount-${index}`);
                if (countElement) {
                    if (eventsInStage > 0) {
                        countElement.textContent = `${eventsInStage}个事件`;
                        countElement.style.fontSize = '0.8em';
                        countElement.style.opacity = '0.8';
                        countElement.style.marginTop = '5px';
                    } else {
                        countElement.textContent = '';
                    }
                }
            });
        }
        
        // 查看阶段历史
        function viewStageHistory(stageIndex) {
            const stage = lifeStages[stageIndex];
            const minAge = stageIndex * 10;
            const maxAge = (stageIndex + 1) * 10 - 1;
            
            const stageEvents = gameState.storyHistory.filter(story => 
                story.age >= minAge && story.age <= maxAge
            );
            
            if (stageEvents.length === 0) {
                alert(`${stage.name}阶段暂无事件记录`);
                return;
            }
            
            const modalContent = `
                <div style="max-width: 600px; max-height: 80vh; overflow-y: auto;">
                    <h3>${stage.name}阶段 (${stage.ageRange})</h3>
                    <div style="margin: 15px 0;">
                        ${stageEvents.map(event => `
                            <div style="margin: 15px 0; padding: 15px; background: #f8f9fa; border-radius: 8px; border-left: 4px solid #667eea;">
                                <h4>${event.title}</h4>
                                <p><strong>年龄：</strong>${event.age}岁</p>
                                <div style="margin: 10px 0; line-height: 1.6;">
                                    ${marked.parse(event.story)}
                                </div>
                                ${gameState.choiceHistory[event.eventId] ? `<p><strong>选择：</strong>${gameState.choiceHistory[event.eventId]}</p>` : ''}
                            </div>
                        `).join('')}
                    </div>
                    <button onclick="closeStageModal()" style="background: #6c5ce7; color: white; border: none; padding: 10px 20px; border-radius: 5px; cursor: pointer; margin-top: 15px;">关闭</button>
                </div>
            `;
            
            showModal(`${stage.name}阶段历史`, modalContent);
        }
        
        // 加载AI配置
        function loadAIConfig() {
            // 优先从localStorage获取命运轨迹专用的AI配置
            let savedConfig = localStorage.getItem('destinyAiConfig');
            if (savedConfig) {
                aiConfig = JSON.parse(savedConfig);
                updateConfigStatus();
                return;
            }
            
            // 如果没有专用配置，尝试从通用AI配置获取
            savedConfig = localStorage.getItem('aiConfig');
            if (savedConfig) {
                aiConfig = JSON.parse(savedConfig);
                updateConfigStatus();
                return;
            }
            
            // 都没有配置，显示未配置状态
            updateConfigStatus();
        }
        
        // 更新配置状态显示
        function updateConfigStatus() {
            const configStatus = document.getElementById('configStatus');
            const configBtn = document.getElementById('configBtn');
            
            if (aiConfig && aiConfig.api_key) {
                configStatus.innerHTML = `<span style="color: #00b894;">${aiConfig.provider} - 已配置</span>`;
                configBtn.textContent = '⚙️ 修改配置';
            } else {
                configStatus.innerHTML = '<span style="color: #ff6b6b;">未配置</span>';
                configBtn.textContent = '⚙️ 配置AI';
            }
        }
        
        // 显示AI配置界面
        function showAIConfig() {
            const modal = document.getElementById('aiConfigModal');
            
            // 如果已有配置，填入当前值
            if (aiConfig && aiConfig.api_key) {
                document.getElementById('modalAiProvider').value = aiConfig.provider || 'openai';
                document.getElementById('modalAiModel').value = aiConfig.model || 'gpt-3.5-turbo';
                document.getElementById('modalAiApiKey').value = aiConfig.api_key || '';
                document.getElementById('modalAiApiUrl').value = aiConfig.api_url || '';
                document.getElementById('modalCustomModel').value = '';
                document.getElementById('modalCustomHeaders').value = aiConfig.custom_headers ? JSON.stringify(aiConfig.custom_headers, null, 2) : '';
            }
            
            updateModalAIConfig();
            modal.style.display = 'flex';
        }
        
        // 隐藏AI配置界面
        function hideAIConfig() {
            document.getElementById('aiConfigModal').style.display = 'none';
        }
        
        // 更新模态框中的AI配置选项
        function updateModalAIConfig() {
            const provider = document.getElementById('modalAiProvider').value;
            const modelSelect = document.getElementById('modalAiModel');
            const customModelGroup = document.getElementById('modalCustomModelGroup');
            const customHeaderGroup = document.getElementById('modalCustomHeaderGroup');
            const apiUrlInput = document.getElementById('modalAiApiUrl');
            
            // 更新模型选项
            if (provider === 'openai') {
                modelSelect.innerHTML = `
                    <option value="gpt-3.5-turbo">GPT-3.5 Turbo</option>
                    <option value="gpt-4">GPT-4</option>
                    <option value="gpt-4-turbo">GPT-4 Turbo</option>
                    <option value="gpt-4o">GPT-4o</option>
                `;
                apiUrlInput.placeholder = "留空使用默认: https://api.openai.com/v1/chat/completions";
            } else if (provider === 'claude') {
                modelSelect.innerHTML = `
                    <option value="claude-3-sonnet-20240229">Claude 3 Sonnet</option>
                    <option value="claude-3-opus-20240229">Claude 3 Opus</option>
                    <option value="claude-3-haiku-20240307">Claude 3 Haiku</option>
                `;
                apiUrlInput.placeholder = "留空使用默认: https://api.anthropic.com/v1/messages";
            } else if (provider === 'deepseek') {
                modelSelect.innerHTML = `
                    <option value="deepseek-chat">DeepSeek Chat</option>
                    <option value="deepseek-coder">DeepSeek Coder</option>
                `;
                apiUrlInput.placeholder = "留空使用默认: https://api.deepseek.com/v1/chat/completions";
            } else if (provider === 'custom') {
                modelSelect.innerHTML = `
                    <option value="gpt-3.5-turbo">GPT-3.5 Turbo</option>
                    <option value="gpt-4">GPT-4</option>
                    <option value="custom-model">自定义模型</option>
                `;
                apiUrlInput.placeholder = "必填: 输入完整的API地址";
            }
            
            // 显示/隐藏自定义选项
            if (provider === 'custom') {
                customModelGroup.style.display = 'block';
                customHeaderGroup.style.display = 'block';
            } else {
                customModelGroup.style.display = 'none';
                customHeaderGroup.style.display = 'none';
            }
        }
        
        // 保存AI配置
        function saveAIConfig() {
            const provider = document.getElementById('modalAiProvider').value;
            const model = document.getElementById('modalAiModel').value;
            const apiKey = document.getElementById('modalAiApiKey').value;
            const apiUrl = document.getElementById('modalAiApiUrl').value;
            const customModel = document.getElementById('modalCustomModel').value;
            const customHeaders = document.getElementById('modalCustomHeaders').value;
            
            if (!apiKey.trim()) {
                alert('请输入API Key');
                return;
            }
            
            if (provider === 'custom' && !apiUrl.trim()) {
                alert('自定义服务商必须填写API地址');
                return;
            }
            
            // 构建配置对象
            let finalModel = model;
            aiConfig = {
                provider: provider,
                model: finalModel,
                api_key: apiKey.trim()
            };
            
            if (provider === 'custom') {
                if (model === 'custom-model' && customModel.trim()) {
                    finalModel = customModel.trim();
                    aiConfig.model = finalModel;
                }
                aiConfig.api_url = apiUrl.trim();
                
                if (customHeaders.trim()) {
                    try {
                        aiConfig.custom_headers = JSON.parse(customHeaders.trim());
                    } catch (e) {
                        alert('自定义Headers格式错误，请检查JSON格式');
                        return;
                    }
                }
            } else if (apiUrl.trim()) {
                aiConfig.api_url = apiUrl.trim();
            }
            
            // 保存配置
            localStorage.setItem('destinyAiConfig', JSON.stringify(aiConfig));
            
            // 更新状态显示
            updateConfigStatus();
            
            // 隐藏模态框
            hideAIConfig();
            
            alert('AI配置保存成功！');
        }
        
        // 生成下一个故事
        async function generateNextStory() {
            try {
                // 检查AI配置
                if (!aiConfig || !aiConfig.api_key) {
                    const storyContent = document.getElementById('storyContent');
                    storyContent.innerHTML = `
                        <div style="text-align: center; padding: 40px; color: #e74c3c;">
                            <h3>⚠️ 需要配置AI</h3>
                            <p>请先配置AI API信息才能开始游戏</p>
                            <button onclick="showAIConfig()" style="background: linear-gradient(45deg, #FF6B6B, #4ECDC4); color: white; border: none; padding: 15px 30px; border-radius: 8px; cursor: pointer; font-size: 1.1em; margin-top: 15px;">
                                🤖 立即配置
                            </button>
                        </div>
                    `;
                    return;
                }
                
                // 显示加载状态
                const storyContent = document.getElementById('storyContent');
                const choicesSection = document.getElementById('choicesSection');
                
                storyContent.innerHTML = `
                    <div class="loading">
                        <div class="loading-spinner"></div>
                        AI正在根据您的命理特征生成剧情...
                    </div>
                `;
                choicesSection.style.display = 'none';
                
                // 构建AI提示词
                const prompt = buildStoryPrompt();
                
                // 使用流式API
                const response = await fetch('/api/destiny-story-stream', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        prompt: prompt,
                        ai_config: aiConfig,
                        game_state: gameState
                    })
                });
                
                if (!response.ok) {
                    throw new Error(`HTTP错误: ${response.status}`);
                }
                
                // 处理流式响应
                const reader = response.body.getReader();
                const decoder = new TextDecoder();
                let fullContent = '';
                let storyData = null;
                
                // 初始化流式显示
                storyContent.innerHTML = `
                    <div style="padding: 20px;">
                        <h3 id="storyTitle" style="color: #667eea; font-size: 1.5em; margin-bottom: 20px; display: none;"></h3>
                        <div id="streamingContent" style="line-height: 1.8; white-space: pre-wrap;"></div>
                        <span id="streamingCursor" style="background: #ffd700; color: #f093fb; padding: 2px 4px; animation: blink 1s infinite;">|</span>
                    </div>
                `;
                
                const streamingContentDiv = document.getElementById('streamingContent');
                const streamingCursor = document.getElementById('streamingCursor');
                
                while (true) {
                    const { done, value } = await reader.read();
                    
                    if (done) {
                        break;
                    }
                    
                    const chunk = decoder.decode(value, { stream: true });
                    const lines = chunk.split('\n');
                    
                    for (const line of lines) {
                        if (line.startsWith('data: ')) {
                            const data = line.slice(6);
                            if (data.trim() === '') continue;
                            
                            try {
                                const parsed = JSON.parse(data);
                                
                                if (parsed.error) {
                                    throw new Error(parsed.error);
                                }
                                
                                if (parsed.content) {
                                    fullContent += parsed.content;
                                    
                                    // 尝试解析是否包含完整的JSON
                                    try {
                                        const jsonMatch = fullContent.match(/```json\s*({.*?})\s*```/s);
                                        if (jsonMatch) {
                                            const storyJson = JSON.parse(jsonMatch[1]);
                                            if (storyJson.title && storyJson.story) {
                                                // 显示标题
                                                const titleEl = document.getElementById('storyTitle');
                                                titleEl.textContent = storyJson.title;
                                                titleEl.style.display = 'block';
                                                
                                                // 只显示故事内容，不显示choices
                                                streamingContentDiv.textContent = storyJson.story;
                                                return;
                                            }
                                        }
                                    } catch (e) {
                                        // 继续处理流式内容
                                    }
                                    
                                    // 过滤掉JSON代码块和choices内容
                                    let displayContent = fullContent;
                                    if (displayContent.includes('```json')) {
                                        displayContent = displayContent.replace(/```json[\s\S]*?```/g, '');
                                    }
                                    
                                    streamingContentDiv.textContent = displayContent;
                                }
                                
                                if (parsed.story_data) {
                                    storyData = parsed.story_data;
                                    
                                    // 确保显示标题
                                    const titleEl = document.getElementById('storyTitle');
                                    if (storyData.title) {
                                        titleEl.textContent = storyData.title;
                                        titleEl.style.display = 'block';
                                    }
                                    
                                    // 只显示故事内容，不显示choices
                                    streamingContentDiv.textContent = storyData.story;
                                }
                                
                                if (parsed.done && storyData) {
                                    // 隐藏流式光标
                                    streamingCursor.style.display = 'none';
                                    
                                    // 延迟一下显示选择按钮
                                    setTimeout(() => {
                                        displayChoices(storyData);
                                    }, 1500);
                                    break;
                                }
                            } catch (e) {
                                console.error('解析JSON错误:', e);
                                continue;
                            }
                        }
                    }
                }
                
                if (!storyData) {
                    throw new Error('未能获取完整的故事数据');
                }
                
            } catch (error) {
                console.error('生成故事失败:', error);
                document.getElementById('storyContent').innerHTML = `
                    <div style="color: #ff6b6b; text-align: center; padding: 20px;">
                        ❌ 故事生成失败: ${error.message}
                        <br><br>
                        <button onclick="generateNextStory()" style="background: #6c5ce7; color: white; border: none; padding: 10px 20px; border-radius: 5px; cursor: pointer;">
                            🔄 重试
                        </button>
                        <button onclick="showAIConfig()" style="background: #e17055; color: white; border: none; padding: 10px 20px; border-radius: 5px; cursor: pointer; margin-left: 10px;">
                            ⚙️ 检查配置
                        </button>
                    </div>
                `;
            }
        }
        
        // 只显示选择按钮的函数
        function displayChoices(storyData) {
            const choicesSection = document.getElementById('choicesSection');
            const choicesList = document.getElementById('choicesList');
            
            // 随机化选择数量和命运值
            let choices = [...storyData.choices];
            
            // 随机化选择数量（2-4个）
            const choiceCount = Math.min(choices.length, Math.floor(Math.random() * 3) + 2);
            
            // 随机打乱选择顺序
            for (let i = choices.length - 1; i > 0; i--) {
                const j = Math.floor(Math.random() * (i + 1));
                [choices[i], choices[j]] = [choices[j], choices[i]];
            }
            
            // 只取前 choiceCount 个选择
            choices = choices.slice(0, choiceCount);
            
            // 随机化命运值
            choices.forEach(choice => {
                const baseImpact = choice.destiny_impact || 0;
                const randomVariation = Math.floor(Math.random() * 6) - 3; // -3 到 +3
                choice.destiny_impact = Math.max(-10, Math.min(10, baseImpact + randomVariation));
            });
            
            // 检查是否应该结束游戏
            const shouldEndGame = gameState.currentAge >= 80 || gameState.destinyScore <= 0;
            
            if (shouldEndGame) {
                // 显示游戏结束按钮
                choicesList.innerHTML = `
                    <button class="choice-button" onclick="endGame()" style="background: linear-gradient(45deg, #ff6b6b, #ee5a24);">
                        <span class="choice-number">✨</span>
                        查看人生总结和盖棺定论
                    </button>
                `;
            } else {
                // 显示正常选择（隐藏命运值）
                choicesList.innerHTML = choices.map((choice, index) => `
                    <button class="choice-button" onclick="makeChoice(${index}, '${choice.text.replace(/'/g, '\\'')}', ${choice.destiny_impact}, '${storyData.title.replace(/'/g, '\\'')}')">
                        <span class="choice-number">${index + 1}</span>
                        ${choice.text}
                        <br>
                        <small style="opacity: 0.8; margin-left: 35px;">预期影响: ${choice.consequence}</small>
                    </button>
                `).join('');
            }
            
            choicesSection.style.display = 'block';
            
            // 保存当前故事
            if (!gameState.storyHistory.find(s => s.title === storyData.title && s.age === gameState.currentAge)) {
                gameState.storyHistory.push({
                    ...storyData,
                    age: gameState.currentAge,
                    eventId: gameState.eventCount,
                    timestamp: new Date().toISOString()
                });
            }
        }
        
        // 构建故事提示词
        function buildStoryPrompt() {
            const birthInfo = gameState.baziData.birth_info;
            const baziAnalysis = gameState.baziData.bazi_analysis;
            const shengxiaoAnalysis = gameState.baziData.shengxiao_analysis;
            const currentStage = lifeStages[Math.floor(gameState.currentAge / 10)] || lifeStages[0];
            const storyStyle = gameState.settings.storyStyle;
            const startTime = gameState.settings.startTime;
            
            // 根据故事风格生成不同的背景设定
            let styleContext = '';
            let styleRequirements = '';
            
            switch (storyStyle) {
                case 'realistic':
                    const birthYear = parseInt(birthInfo.date.split('-')[0]);
                    const currentYear = startTime === 'current' ? new Date().getFullYear() : birthYear + gameState.currentAge;
                    
                    styleContext = `
## 故事背景设定：
- 故事风格：现实写实
- 时代背景：${currentYear}年
- 历史环境：请结合${currentYear}年前后的真实历史事件和社会背景
- 开始时间：${startTime === 'birth' ? '从出生开始' : '从现在开始'}`;
                    
                    styleRequirements = `
- 故事必须基于真实的历史事件和社会背景
- 融入${currentYear}年代的时代特色（科技、文化、政治环境等）
- 事件要符合当时的社会现实和历史可能性
- 如果涉及重大历史事件，要保持历史准确性
- 故事不必过于玄乎，随机事件不应太统一类型，大事件也有可能是黑天鹅事件，比如疫情、地震、经济危机、战争、科技变革等`;
                    break;
                    
                case 'fantasy':
                    styleContext = `
## 故事背景设定：
- 故事风格：奇幻冒险
- 世界观：命理法则化为魔法力量的奇幻世界
- 八字对应：五行成为魔法元素，十神化为天赋技能
- 神煞系统：古代神煞成为神秘力量的源泉`;
                    
                    styleRequirements = `
- 将八字命理概念融入奇幻元素（如五行魔法、命格天赋等）
- 创造充满想象力的奇幻场景和生物
- 保持命理学逻辑的同时，增加魔法冒险元素
- 故事要有奇幻色彩但仍体现人生选择的重要性`;
                    break;
                    
                case 'historical':
                    styleContext = `
## 故事背景设定：
- 故事风格：古代传奇
- 时代背景：中国古代传统社会
- 文化环境：传统命理学盛行的时代
- 社会结构：古代的家族、官场、江湖体系`;
                    
                    styleRequirements = `
- 故事发生在中国古代，体现传统文化和社会制度
- 融入古代的科举、家族、师承等社会元素
- 使用符合古代背景的语言风格和情节设定
- 强调传统命理学在古代社会中的作用和影响`;
                    break;
                    
                case 'modern':
                    styleContext = `
## 故事背景设定：
- 故事风格：都市传说
- 时代背景：现代都市
- 科技环境：命理与现代科技的神秘融合
- 社会背景：现代都市中隐藏的命理秘密`;
                    
                    styleRequirements = `
- 故事设定在现代都市环境中
- 将传统命理学与现代科技巧妙结合
- 创造都市传说般的神秘氛围
- 体现现代人生活中的命理元素和选择`;
                    break;
            }
            
            return `你是一位精通命理学的游戏剧情设计师，请基于以下八字信息为用户创建一个命运轨迹游戏的情节。

## 用户八字信息：
- 出生日期：${birthInfo.date} (${birthInfo.calendar_type})
- 出生时辰：${birthInfo.time}点
- 性别：${birthInfo.gender}
- 生肖：${birthInfo.shengxiao}

## 八字分析：
${baziAnalysis}

## 生肖分析：
年支：${shengxiaoAnalysis.year_zhi}
相合生肖：${JSON.stringify(shengxiaoAnalysis.compatible)}
相冲生肖：${JSON.stringify(shengxiaoAnalysis.incompatible)}

${styleContext}

## 当前游戏状态：
- 当前年龄：${gameState.currentAge}岁
- 人生阶段：${currentStage.name} (${currentStage.description})
- 命运值：${gameState.destinyScore}
- 已发生事件：${gameState.eventCount}个

## 历史选择记录：
${gameState.choiceHistory.length > 0 ? gameState.choiceHistory.map((choice, index) => `${index + 1}. ${choice}`).join('\n') : '暂无历史选择'}

## 任务要求：
请创建一个符合用户命理特征的人生情节，包含：

1. **情节描述**：根据用户的八字特点和当前年龄阶段，描述一个具体的人生事件或情况
2. **3-4个选择项**：提供3-4个不同的应对方式，体现不同的人生态度和价值观

## 故事风格要求：
${styleRequirements}

## 输出格式：
请严格按照以下JSON格式输出：

\`\`\`json
{
  "title": "情节标题",
  "story": "详细的故事描述，要生动有趣，符合命理特征和故事风格",
  "choices": [
    {
      "text": "选择1的描述",
      "consequence": "这个选择可能带来的结果",
      "destiny_impact": 5
    },
    {
      "text": "选择2的描述", 
      "consequence": "这个选择可能带来的结果",
      "destiny_impact": -3
    },
    {
      "text": "选择3的描述",
      "consequence": "这个选择可能带来的结果", 
      "destiny_impact": 0
    }
  ]
}
\`\`\`

要求：
- 故事要符合用户的八字命理特征和选择的故事风格
- 选择要有明确的后果预示
- destiny_impact范围在-10到+10之间，但不应固定顺序和数值，要随机分布
- 语言要生动有趣，适合游戏体验
- 体现命理学中的因果关系`;
        }
        
        // 做出选择
        async function makeChoice(choiceIndex, choiceText, destinyImpact, storyTitle) {
            try {
                // 禁用所有选择按钮
                const choiceButtons = document.querySelectorAll('.choice-button');
                choiceButtons.forEach(btn => btn.disabled = true);
                
                // 记录选择
                gameState.choiceHistory.push(choiceText);
                gameState.destinyScore += destinyImpact;
                gameState.eventCount++;
                gameState.currentAge += Math.floor(Math.random() * 3) + 1; // 随机增加1-3岁
                
                // 更新显示
                updateGameStats();
                updateCurrentStage();
                updateHistoryDisplay();
                updateStageEventCounts();
                
                // 延迟后生成下一个故事
                setTimeout(async () => {
                    await generateNextStory();
                }, 2000);
                
            } catch (error) {
                console.error('处理选择失败:', error);
                alert('处理选择时发生错误，请重试');
            }
        }
        
        // 更新游戏统计
        function updateGameStats() {
            document.getElementById('currentAge').textContent = `${gameState.currentAge}岁`;
            document.getElementById('destinyScore').textContent = gameState.destinyScore;
            document.getElementById('eventCount').textContent = gameState.eventCount;
        }
        
        // 更新当前阶段
        function updateCurrentStage() {
            const stageIndex = Math.min(Math.floor(gameState.currentAge / 10), lifeStages.length - 1);
            
            // 更新当前阶段样式
            document.querySelectorAll('.life-stage').forEach((stage, index) => {
                stage.classList.toggle('current', index === stageIndex);
            });
            
            gameState.currentStage = lifeStages[stageIndex].name;
        }
        
        // 更新历史记录显示
        function updateHistoryDisplay() {
            const historyContent = document.getElementById('historyContent');
            
            if (gameState.storyHistory.length === 0) {
                historyContent.innerHTML = '<div class="empty-history">暂无历史记录</div>';
                return;
            }
            
            const historyHTML = gameState.storyHistory.map((story, index) => {
                const choice = gameState.choiceHistory[index];
                return `
                    <div class="history-item" onclick="viewHistoryItem(${index})">
                        <div class="history-item-title">${story.title}</div>
                        ${choice ? `<div class="history-item-choice">选择: ${choice}</div>` : ''}
                        <div class="history-item-age">${story.age}岁时</div>
                    </div>
                `;
            }).join('');
            
            historyContent.innerHTML = historyHTML;
        }
        
        // 查看历史事件详情
        function viewHistoryItem(index) {
            const story = gameState.storyHistory[index];
            const choice = gameState.choiceHistory[index];
            
            const modalContent = `
                <div style="max-width: 600px; max-height: 80vh; overflow-y: auto;">
                    <h3>${story.title}</h3>
                    <p><strong>年龄：</strong>${story.age}岁</p>
                    <div style="margin: 15px 0; padding: 15px; background: #f8f9fa; border-radius: 8px;">
                        ${marked.parse(story.story)}
                    </div>
                    ${choice ? `<p><strong>您的选择：</strong>${choice}</p>` : ''}
                    <div style="margin-top: 20px; display: flex; gap: 10px;">
                        <button onclick="closeHistoryModal()" style="background: #6c5ce7; color: white; border: none; padding: 10px 20px; border-radius: 5px; cursor: pointer;">关闭</button>
                        ${choice ? `<button onclick="modifyChoice(${index})" style="background: #e17055; color: white; border: none; padding: 10px 20px; border-radius: 5px; cursor: pointer;">修改选择</button>` : ''}
                    </div>
                </div>
            `;
            
            showModal('历史回顾', modalContent);
        }
        
        // 显示模态框
        function showModal(title, content) {
            const modal = document.createElement('div');
            modal.id = 'historyModal';
            modal.style.cssText = `
                position: fixed; top: 0; left: 0; width: 100%; height: 100%;
                background: rgba(0,0,0,0.8); z-index: 2000;
                display: flex; align-items: center; justify-content: center;
                padding: 20px;
            `;
            
            modal.innerHTML = `
                <div style="background: white; border-radius: 15px; padding: 30px; position: relative;">
                    <h2 style="margin-bottom: 20px; color: #333;">${title}</h2>
                    ${content}
                </div>
            `;
            
            document.body.appendChild(modal);
            
            // 点击背景关闭
            modal.addEventListener('click', function(e) {
                if (e.target === modal) {
                    closeHistoryModal();
                }
            });
        }
        
        // 关闭历史模态框
        function closeHistoryModal() {
            const modal = document.getElementById('historyModal');
            if (modal) {
                modal.remove();
            }
        }
        
        // 关闭阶段模态框
        function closeStageModal() {
            const modal = document.getElementById('historyModal');
            if (modal) {
                modal.remove();
            }
        }
        
        // 修改历史选择
        function modifyChoice(eventIndex) {
            if (confirm('确定要修改这个选择吗？这将重置后续所有事件，游戏将从此节点重新开始。')) {
                closeHistoryModal();
                
                // 重置游戏状态到指定事件之前
                const targetEvent = gameState.storyHistory[eventIndex];
                
                // 保留到指定事件的所有状态
                gameState.storyHistory = gameState.storyHistory.slice(0, eventIndex + 1);
                gameState.choiceHistory = gameState.choiceHistory.slice(0, eventIndex);
                
                // 恢复到该事件时的状态
                gameState.currentAge = targetEvent.age;
                gameState.eventCount = eventIndex;
                
                // 重新计算命运值（基于保留的选择）
                gameState.destinyScore = 100;
                for (let i = 0; i < gameState.choiceHistory.length; i++) {
                    // 这里简化处理，实际应该记录每个选择的影响值
                    const randomImpact = Math.floor(Math.random() * 6) - 2; // -2 到 +3
                    gameState.destinyScore += randomImpact;
                }
                
                // 更新显示
                updateGameStats();
                updateCurrentStage();
                updateHistoryDisplay();
                updateStageEventCounts();
                
                // 重新生成该事件的选择
                restoreEventChoices(targetEvent, eventIndex);
            }
        }
        
        // 恢复事件选择界面
        function restoreEventChoices(storyData, eventIndex) {
            // 显示故事
            document.getElementById('gameSettings').style.display = 'none';
            document.getElementById('storySection').style.display = 'block';
            document.getElementById('storyTitle').textContent = storyData.title;
            document.getElementById('storyContent').innerHTML = `
                <div style="padding: 20px; line-height: 1.8;">
                    ${marked.parse(storyData.story)}
                </div>
            `;
            
            // 重新生成选择（模拟原始选择）
            const choices = [
                {
                    text: "积极应对，寻求最佳解决方案",
                    consequence: "可能带来正面影响",
                    destiny_impact: Math.floor(Math.random() * 6) + 2
                },
                {
                    text: "谨慎处理，避免冒险",
                    consequence: "保持现状，小幅改善",
                    destiny_impact: Math.floor(Math.random() * 4) - 1
                },
                {
                    text: "随遇而安，顺其自然",
                    consequence: "影响不确定",
                    destiny_impact: Math.floor(Math.random() * 4) - 2
                },
                {
                    text: "另辟蹊径，寻找新机会",
                    consequence: "有风险但可能有回报",
                    destiny_impact: Math.floor(Math.random() * 8) - 3
                }
            ];
            
            // 随机选择2-4个选项
            const choiceCount = Math.floor(Math.random() * 3) + 2;
            const selectedChoices = choices.sort(() => Math.random() - 0.5).slice(0, choiceCount);
            
            const choicesSection = document.getElementById('choicesSection');
            const choicesList = document.getElementById('choicesList');
            
            choicesList.innerHTML = selectedChoices.map((choice, index) => `
                <button class="choice-button" onclick="makeChoice(${index}, '${choice.text.replace(/'/g, '\\'')}', ${choice.destiny_impact}, '${storyData.title.replace(/'/g, '\\'')}')">
                    <span class="choice-number">${index + 1}</span>
                    ${choice.text}
                    <br>
                    <small style="opacity: 0.8; margin-left: 35px;">预期影响: ${choice.consequence}</small>
                </button>
            `).join('');
            
            choicesSection.style.display = 'block';
        }
        
        // 结束游戏并生成完整故事总结
        async function endGame() {
            try {
                // 检查AI配置
                if (!aiConfig || !aiConfig.api_key) {
                    alert('需要配置AI才能生成人生总结');
                    return;
                }
                
                // 显示加载状态
                document.getElementById('choicesSection').style.display = 'none';
                document.getElementById('storyContent').innerHTML = `
                    <div class="loading">
                        <div class="loading-spinner"></div>
                        正在生成您的人生总结和盖棺定论...
                    </div>
                `;
                
                // 构建总结提示词
                const summaryPrompt = buildSummaryPrompt();
                
                // 调用AI生成总结
                const response = await fetch('/api/destiny-story-stream', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        prompt: summaryPrompt,
                        ai_config: aiConfig,
                        game_state: gameState
                    })
                });
                
                if (!response.ok) {
                    throw new Error(`HTTP错误: ${response.status}`);
                }
                
                // 处理流式响应
                const reader = response.body.getReader();
                const decoder = new TextDecoder();
                let fullContent = '';
                
                // 初始化流式显示
                document.getElementById('storyContent').innerHTML = `
                    <div style="padding: 20px;">
                        <h2 style="color: #667eea; text-align: center; margin-bottom: 30px;">📜 人生总结</h2>
                        <div id="summaryContent" style="line-height: 1.8; white-space: pre-wrap;"></div>
                        <span id="summaryCursor" style="background: #ffd700; color: #f093fb; padding: 2px 4px; animation: blink 1s infinite;">|</span>
                    </div>
                `;
                
                const summaryContentDiv = document.getElementById('summaryContent');
                const summaryCursor = document.getElementById('summaryCursor');
                
                while (true) {
                    const { done, value } = await reader.read();
                    
                    if (done) {
                        break;
                    }
                    
                    const chunk = decoder.decode(value, { stream: true });
                    const lines = chunk.split('\n');
                    
                    for (const line of lines) {
                        if (line.startsWith('data: ')) {
                            const data = line.slice(6);
                            if (data.trim() === '') continue;
                            
                            try {
                                const parsed = JSON.parse(data);
                                
                                if (parsed.error) {
                                    throw new Error(parsed.error);
                                }
                                
                                if (parsed.content) {
                                    fullContent += parsed.content;
                                    summaryContentDiv.textContent = fullContent;
                                }
                                
                                if (parsed.done) {
                                    summaryCursor.style.display = 'none';
                                    
                                    // 添加重新开始按钮
                                    setTimeout(() => {
                                        summaryContentDiv.innerHTML += `
                                            <div style="text-align: center; margin-top: 30px; padding-top: 20px; border-top: 2px solid #eee;">
                                                <button onclick="resetGame()" style="background: linear-gradient(45deg, #667eea, #764ba2); color: white; border: none; padding: 15px 30px; border-radius: 8px; cursor: pointer; font-size: 1.1em; margin-right: 15px;">
                                                    🔄 重新开始人生
                                                </button>
                                                <button onclick="window.close()" style="background: linear-gradient(45deg, #ff6b6b, #ee5a24); color: white; border: none; padding: 15px 30px; border-radius: 8px; cursor: pointer; font-size: 1.1em;">
                                                    🏠 返回主页
                                                </button>
                                            </div>
                                        `;
                                    }, 2000);
                                    break;
                                }
                            } catch (e) {
                                console.error('解析JSON错误:', e);
                                continue;
                            }
                        }
                    }
                }
                
            } catch (error) {
                console.error('生成人生总结失败:', error);
                document.getElementById('storyContent').innerHTML = `
                    <div style="color: #ff6b6b; text-align: center; padding: 20px;">
                        ❌ 生成人生总结失败: ${error.message}
                        <br><br>
                        <button onclick="endGame()" style="background: #6c5ce7; color: white; border: none; padding: 10px 20px; border-radius: 5px; cursor: pointer;">
                            🔄 重试
                        </button>
                    </div>
                `;
            }
        }
        
        // 构建总结提示词
        function buildSummaryPrompt() {
            const birthInfo = gameState.baziData.birth_info;
            const baziAnalysis = gameState.baziData.bazi_analysis;
            
            // 组织人生事件
            const lifeEvents = gameState.storyHistory.map((story, index) => {
                const choice = gameState.choiceHistory[index];
                return `**${story.age}岁 - ${story.title}**\n${story.story}\n${choice ? `选择：${choice}` : ''}`;
            }).join('\n\n');
            
            return `你是一位德高望重的命理大师，现在需要为一个人的完整人生做出总结和盖棺定论。

## 基本信息：
- 出生日期：${birthInfo.date}
- 性别：${birthInfo.gender}
- 生肖：${birthInfo.shengxiao}

## 八字分析：
${baziAnalysis}

## 人生轨迹：
${lifeEvents}

## 人生数据：
- 最终年龄：${gameState.currentAge}岁
- 命运值：${gameState.destinyScore}
- 经历事件：${gameState.eventCount}个
- 故事风格：${gameState.settings.storyStyle}

## 任务要求：
请基于以上信息，写一篇深刻的人生总结，包含：

1. **人生回顾**：总结这个人的主要人生阶段和关键事件
2. **性格特点**：基于八字分析和人生选择，分析其性格特征
3. **成就与遗憾**：客观评价人生的成功和不足
4. **人生感悟**：从命理角度分析人生的因果关系
5. **盖棺定论**：给出最终的人生评价和启示

## 写作要求：
- 语言优美，富有哲理
- 结合命理学知识，体现因果关系
- 既要客观公正，又要充满人文关怀
- 篇幅适中，约500-800字
- 以第三人称叙述，语调庄重而温暖

请以纯文本形式输出，不需要JSON格式。`;
        }
        
        // 重置游戏
        function resetGame() {
            if (confirm('确定要重新开始游戏吗？所有进度将丢失。')) {
                gameState = {
                    baziData: gameState.baziData, // 保留八字数据
                    currentAge: 0,
                    currentStage: '童年',
                    destinyScore: 100,
                    eventCount: 0,
                    choiceHistory: [],
                    storyHistory: [],
                    settings: gameState.settings // 保留游戏设置
                };
                
                updateGameStats();
                generateLifeStages();
                updateHistoryDisplay();
                
                // 显示设置界面，重新选择
                document.getElementById('gameSettings').style.display = 'block';
                document.getElementById('storySection').style.display = 'none';
                document.getElementById('choicesSection').style.display = 'none';
            }
        }
        
        // 页面加载完成后初始化游戏
        window.addEventListener('load', initGame);
    </script>
</body>
</html>