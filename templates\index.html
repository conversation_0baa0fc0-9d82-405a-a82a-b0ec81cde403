<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>八字生肖分析系统</title>
    <!-- Markdown解析库 -->
    <script src="https://cdn.jsdelivr.net/npm/marked@9.1.6/marked.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(45deg, #FF6B6B, #4ECDC4);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .header p {
            font-size: 1.1em;
            opacity: 0.9;
        }
        
        .form-section {
            padding: 40px;
            background: #f8f9fa;
        }
        
        .form-group {
            margin-bottom: 25px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: bold;
            color: #333;
            font-size: 1.1em;
        }
        
        .form-group input, .form-group select {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid #ddd;
            border-radius: 8px;
            font-size: 1em;
            transition: border-color 0.3s ease;
        }
        
        .form-group input:focus, .form-group select:focus {
            outline: none;
            border-color: #4ECDC4;
            box-shadow: 0 0 0 3px rgba(78, 205, 196, 0.1);
        }
        
        .form-row {
            display: flex;
            gap: 20px;
        }
        
        .form-row .form-group {
            flex: 1;
        }
        
        .submit-btn {
            background: linear-gradient(45deg, #FF6B6B, #4ECDC4);
            color: white;
            border: none;
            padding: 15px 40px;
            font-size: 1.2em;
            border-radius: 50px;
            cursor: pointer;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            display: block;
            margin: 30px auto 0;
        }
        
        .submit-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.2);
        }
        
        .submit-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }
        
        .copy-btn {
            background: linear-gradient(45deg, #74b9ff, #0984e3);
            color: white;
            border: none;
            padding: 10px 25px;
            font-size: 1em;
            border-radius: 25px;
            cursor: pointer;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            margin: 10px 5px 0 0;
            display: inline-block;
        }
        
        .copy-btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
        }
        
        .copy-btn:active {
            transform: translateY(0);
        }
        
        .copy-success {
            background: linear-gradient(45deg, #00b894, #00a085) !important;
        }
        
        .copy-actions {
            text-align: center;
            margin: 20px 0;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 8px;
        }
        
        .ai-config-section {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        
        .ai-config-toggle {
            background: linear-gradient(45deg, #fdcb6e, #e17055);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 20px;
            cursor: pointer;
            font-size: 0.95em;
            transition: all 0.3s ease;
        }
        
        .ai-config-toggle:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
        }
        
        .ai-config-form {
            display: none;
            margin-top: 15px;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
        }
        
        .ai-config-form.show {
            display: grid;
        }
        
        .ai-interpret-btn {
            background: linear-gradient(45deg, #a29bfe, #6c5ce7);
            color: white;
            border: none;
            padding: 12px 30px;
            font-size: 1.1em;
            border-radius: 25px;
            cursor: pointer;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            margin: 15px 10px 0 0;
            display: inline-block;
        }
        
        .ai-interpret-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0,0,0,0.3);
        }
        
        .ai-interpret-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }
        
        .destiny-track-btn {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 12px 25px;
            font-size: 1em;
            border-radius: 25px;
            cursor: pointer;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            margin: 15px 10px 0 0;
            display: inline-block;
        }
        
        .destiny-track-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(102, 126, 234, 0.3);
        }
        
        .ai-interpretation {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            color: white;
            padding: 25px;
            border-radius: 12px;
            margin: 20px 0;
            box-shadow: 0 8px 32px rgba(240, 147, 251, 0.3);
        }
        
        .ai-interpretation-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }
        
        .ai-interpretation h3 {
            margin: 0;
            font-size: 1.3em;
        }
        
        .ai-collapse-btn {
            background: rgba(255,255,255,0.2);
            border: none;
            color: white;
            padding: 8px 15px;
            border-radius: 20px;
            cursor: pointer;
            font-size: 0.9em;
            transition: all 0.3s ease;
        }
        
        .ai-collapse-btn:hover {
            background: rgba(255,255,255,0.3);
        }
        
        .ai-interpretation .content {
            background: rgba(255,255,255,0.1);
            padding: 20px;
            border-radius: 8px;
            line-height: 1.6;
            transition: all 0.3s ease;
            overflow: hidden;
        }
        
        .ai-interpretation .content.collapsed {
            max-height: 0;
            padding: 0 20px;
            opacity: 0;
        }
        
        .ai-interpretation-content {
            transition: all 0.3s ease;
        }
        
        .ai-interpretation .content h1,
        .ai-interpretation .content h2,
        .ai-interpretation .content h3 {
            color: #fff;
            margin: 15px 0 10px 0;
            border-bottom: 1px solid rgba(255,255,255,0.3);
            padding-bottom: 5px;
        }
        
        .ai-interpretation .content h1 {
            font-size: 1.5em;
        }
        
        .ai-interpretation .content h2 {
            font-size: 1.3em;
        }
        
        .ai-interpretation .content h3 {
            font-size: 1.1em;
        }
        
        .ai-interpretation .content p {
            margin: 10px 0;
            line-height: 1.8;
        }
        
        .ai-interpretation .content ul,
        .ai-interpretation .content ol {
            margin: 10px 0;
            padding-left: 20px;
        }
        
        .ai-interpretation .content li {
            margin: 5px 0;
            line-height: 1.6;
        }
        
        .ai-interpretation .content strong {
            color: #ffd700;
            font-weight: bold;
        }
        
        .ai-interpretation .content em {
            color: #ffc0cb;
            font-style: italic;
        }
        
        .ai-interpretation .content blockquote {
            border-left: 4px solid rgba(255,255,255,0.5);
            padding-left: 15px;
            margin: 15px 0;
            font-style: italic;
            opacity: 0.9;
        }
        
        .ai-interpretation .content code {
            background: rgba(0,0,0,0.3);
            padding: 2px 6px;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
        }
        
        .ai-interpretation .content pre {
            background: rgba(0,0,0,0.3);
            padding: 15px;
            border-radius: 8px;
            overflow-x: auto;
            margin: 15px 0;
        }
        
        .ai-interpretation .content pre code {
            background: none;
            padding: 0;
        }
        
        .streaming-cursor {
            display: inline-block;
            width: 2px;
            height: 1em;
            background: white;
            animation: blink 1s infinite;
        }
        
        @keyframes blink {
            0%, 50% { opacity: 1; }
            51%, 100% { opacity: 0; }
        }
        
        .ai-loading {
            text-align: center;
            padding: 20px;
            color: #6c5ce7;
            font-weight: bold;
        }
        
        @media (max-width: 768px) {
            .ai-config-form {
                grid-template-columns: 1fr;
            }
        }
        
        .results-section {
            padding: 40px;
            display: none;
        }
        
        .result-card {
            background: white;
            border-radius: 12px;
            padding: 25px;
            margin-bottom: 25px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            border-left: 5px solid #4ECDC4;
        }
        
        .result-card h3 {
            color: #333;
            margin-bottom: 15px;
            font-size: 1.4em;
            display: flex;
            align-items: center;
        }
        
        .result-card h3::before {
            content: "🔮";
            margin-right: 10px;
            font-size: 1.2em;
        }
        
        .shengxiao-card h3::before {
            content: "🐲";
        }
        
        .basic-info {
            background: #e3f2fd;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        
        .basic-info h4 {
            color: #1976d2;
            margin-bottom: 10px;
        }
        
        .info-item {
            display: inline-block;
            background: white;
            padding: 8px 15px;
            margin: 5px 10px 5px 0;
            border-radius: 20px;
            border: 1px solid #ddd;
            font-weight: bold;
        }
        
        .bazi-content {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            color: #2c3e50;
            padding: 25px;
            border-radius: 12px;
            font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
            white-space: pre;
            overflow-x: auto;
            font-size: 0.85em;
            line-height: 1.4;
            border: 1px solid #dee2e6;
            box-shadow: inset 0 2px 8px rgba(0,0,0,0.05);
            width: 100%;
            min-height: 500px;
        }
        
        .bazi-container {
            width: 100%;
            overflow-x: auto;
            margin: 15px 0;
        }
        
        .bazi-table {
            width: max-content;
            min-width: 100%;
            font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
            font-size: 0.9em;
            white-space: nowrap;
        }
        
        .bazi-section {
            margin: 20px 0;
            padding: 15px;
            background: rgba(255,255,255,0.7);
            border-radius: 8px;
            border-left: 4px solid #74b9ff;
        }
        
        .bazi-header {
            color: #2d3436;
            font-weight: bold;
            border-bottom: 2px solid #ddd;
            padding-bottom: 8px;
            margin-bottom: 12px;
            font-size: 1.1em;
        }
        
        .bazi-row {
            margin: 8px 0;
            padding: 6px 0;
        }
        
        .bazi-highlight {
            color: #e17055;
            font-weight: bold;
        }
        
        .bazi-number {
            color: #0984e3;
            font-weight: bold;
        }
        
        .bazi-element {
            color: #00b894;
            font-weight: bold;
        }
        
        .shengxiao-details {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-top: 15px;
        }
        
        .compatible, .incompatible {
            background: #f5f5f5;
            padding: 15px;
            border-radius: 8px;
        }
        
        .compatible {
            border-left: 4px solid #4caf50;
        }
        
        .incompatible {
            border-left: 4px solid #f44336;
        }
        
        .compatible h5, .incompatible h5 {
            margin-bottom: 10px;
            font-size: 1.1em;
        }
        
        .compatible h5 {
            color: #4caf50;
        }
        
        .incompatible h5 {
            color: #f44336;
        }
        
        .zodiac-list {
            margin: 5px 0;
        }
        
        .zodiac-list strong {
            color: #666;
        }
        
        .loading {
            text-align: center;
            padding: 40px;
            color: #666;
        }
        
        .loading::after {
            content: "";
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #4ECDC4;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-left: 10px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .error {
            background: #ffebee;
            color: #c62828;
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid #c62828;
            margin: 20px 0;
        }
        
        @media (max-width: 768px) {
            .form-row {
                flex-direction: column;
                gap: 0;
            }
            
            .header h1 {
                font-size: 2em;
            }
            
            .shengxiao-details {
                grid-template-columns: 1fr;
            }
            
            .container {
                margin: 10px;
                border-radius: 10px;
            }
            
            .form-section, .results-section {
                padding: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔮 八字生肖分析系统</h1>
            <p>基于传统命理学，为您提供专业的八字和生肖分析</p>
        </div>
        
        <div class="form-section">
            <form id="analysisForm">
                <div class="form-row">
                    <div class="form-group">
                        <label for="birthYear">出生年份</label>
                        <select id="birthYear" name="birthYear" required></select>
                    </div>
                    <div class="form-group">
                        <label for="birthMonth">出生月份</label>
                        <select id="birthMonth" name="birthMonth" required></select>
                    </div>
                    <div class="form-group">
                        <label for="birthDay">出生日期</label>
                        <select id="birthDay" name="birthDay" required></select>
                    </div>
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="calendarType">历法类型</label>
                        <select id="calendarType" name="calendarType">
                            <option value="农历" selected>农历</option>
                            <option value="公历">公历</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="birthTime">出生时辰</label>
                        <select id="birthTime" name="birthTime">
                            <option value="0">子时 (23:00-01:00)</option>
                            <option value="1">丑时 (01:00-03:00)</option>
                            <option value="3">寅时 (03:00-05:00)</option>
                            <option value="5">卯时 (05:00-07:00)</option>
                            <option value="7">辰时 (07:00-09:00)</option>
                            <option value="8" selected>巳时 (09:00-11:00)</option>
                            <option value="11">午时 (11:00-13:00)</option>
                            <option value="13">未时 (13:00-15:00)</option>
                            <option value="15">申时 (15:00-17:00)</option>
                            <option value="17">酉时 (17:00-19:00)</option>
                            <option value="19">戌时 (19:00-21:00)</option>
                            <option value="21">亥时 (21:00-23:00)</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="gender">性别</label>
                        <select id="gender" name="gender">
                            <option value="男">男</option>
                            <option value="女">女</option>
                        </select>
                    </div>
                </div>
                
                <button type="submit" class="submit-btn" id="submitBtn">开始分析</button>
            </form>
        </div>
        
        <div class="results-section" id="resultsSection">
            <div class="loading" id="loading">正在分析中，请稍候...</div>
            <div id="results" style="display: none;"></div>
        </div>
    </div>

    <script>
        document.getElementById('analysisForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const formData = new FormData(this);
            const year = formData.get('birthYear');
            const month = formData.get('birthMonth').padStart(2, '0');
            const day = formData.get('birthDay').padStart(2, '0');
            
            const data = {
                birth_date: `${year}-${month}-${day}`,
                birth_time: formData.get('birthTime'),
                gender: formData.get('gender'),
                calendar_type: formData.get('calendarType')
            };
            
            // 显示加载状态
            document.getElementById('resultsSection').style.display = 'block';
            document.getElementById('loading').style.display = 'block';
            document.getElementById('results').style.display = 'none';
            document.getElementById('submitBtn').disabled = true;
            document.getElementById('submitBtn').textContent = '分析中...';
            
            // 调用API
            fetch('/api/complete', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(data)
            })
            .then(response => response.json())
            .then(result => {
                document.getElementById('loading').style.display = 'none';
                
                if (result.success) {
                    displayResults(result);
                } else {
                    let errorMsg = result.error || '分析失败，请重试';
                    if (result.debug_info) {
                        errorMsg += `\n\n调试信息:\n命令: ${result.debug_info.command}\n返回码: ${result.debug_info.return_code}\n错误: ${result.debug_info.stderr}`;
                    }
                    displayError(errorMsg);
                }
            })
            .catch(error => {
                document.getElementById('loading').style.display = 'none';
                displayError('网络错误：' + error.message);
            })
            .finally(() => {
                document.getElementById('submitBtn').disabled = false;
                document.getElementById('submitBtn').textContent = '开始分析';
            });
        });
        
        function displayResults(data) {
            // 保存数据到全局变量供复制功能使用
            currentAnalysisData = data;
            
            const resultsDiv = document.getElementById('results');
            const birthInfo = data.birth_info;
            const shengxiaoAnalysis = data.shengxiao_analysis;
            const baziAnalysis = data.bazi_analysis;
            
            resultsDiv.innerHTML = `
                <div class="basic-info">
                    <h4>📋 基本信息</h4>
                    <span class="info-item">📅 ${birthInfo.date} (${birthInfo.calendar_type})</span>
                    <span class="info-item">⏰ ${getTimeLabel(birthInfo.time)}</span>
                    <span class="info-item">👤 ${birthInfo.gender}</span>
                    <span class="info-item">🐲 ${birthInfo.shengxiao}</span>
                </div>
                
                <div class="copy-actions">
                    <button class="copy-btn" onclick="copyShengxiaoInfo()">📋 复制生肖信息</button>
                    <button class="copy-btn" onclick="copyBaziInfo()">📋 复制八字排盘</button>
                    <button class="copy-btn" onclick="copyAllInfo()">📋 复制全部信息</button>
                </div>
                
                <div class="ai-config-section">
                    <h4>🤖 AI智能解读</h4>
                    <p>配置AI API信息，获得专业的命理解读</p>
                    <button class="ai-config-toggle" onclick="toggleAIConfig()">⚙️ 配置AI设置</button>
                    
                    <div class="ai-config-form" id="aiConfigForm">
                        <div class="form-group">
                            <label for="aiProvider">AI服务商</label>
                            <select id="aiProvider" onchange="updateAIConfig()">
                                <option value="openai">OpenAI (GPT)</option>
                                <option value="claude">Anthropic (Claude)</option>
                                <option value="deepseek">DeepSeek</option>
                                <option value="custom">自定义服务商</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="aiModel">AI模型</label>
                            <select id="aiModel">
                                <option value="gpt-3.5-turbo">GPT-3.5 Turbo</option>
                                <option value="gpt-4">GPT-4</option>
                                <option value="gpt-4-turbo">GPT-4 Turbo</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="aiApiKey">API Key</label>
                            <input type="password" id="aiApiKey" placeholder="请输入您的API Key">
                        </div>
                        <div class="form-group">
                            <label for="aiApiUrl">API地址 (可选)</label>
                            <input type="text" id="aiApiUrl" placeholder="自定义API地址，留空使用默认">
                        </div>
                        
                        <div class="form-group custom-only" id="customModelGroup" style="display: none;">
                            <label for="customModel">自定义模型名</label>
                            <input type="text" id="customModel" placeholder="输入自定义模型名称">
                        </div>
                        
                        <div class="form-group custom-only" id="customHeaderGroup" style="display: none;">
                            <label for="customHeaders">自定义Headers (JSON格式，可选)</label>
                            <textarea id="customHeaders" placeholder='{"X-Custom-Header": "value"}' rows="3"></textarea>
                        </div>
                    </div>
                    
                    <button class="ai-interpret-btn" id="aiInterpretBtn" onclick="getAIInterpretation()">
                        🧠 AI智能解读
                    </button>
                    
                    <button class="destiny-track-btn" id="destinyTrackBtn" onclick="openDestinyTrack()" style="display: none;">
                        🎭 命运轨迹
                    </button>
                </div>
                
                <div id="aiInterpretationResult" style="display: none;"></div>
                
                <div class="result-card shengxiao-card">
                    <h3>生肖分析 - ${birthInfo.shengxiao}</h3>
                    <p><strong>年支：</strong>${shengxiaoAnalysis.year_zhi}</p>
                    
                    <div class="shengxiao-details">
                        <div class="compatible">
                            <h5>💕 相合生肖</h5>
                            <div class="zodiac-list"><strong>三合：</strong>${shengxiaoAnalysis.compatible.sanhe.join('、') || '无'}</div>
                            <div class="zodiac-list"><strong>六合：</strong>${shengxiaoAnalysis.compatible.liuhe.join('、') || '无'}</div>
                            <div class="zodiac-list"><strong>三会：</strong>${shengxiaoAnalysis.compatible.sanhui.join('、') || '无'}</div>
                        </div>
                        
                        <div class="incompatible">
                            <h5>⚠️ 不合生肖</h5>
                            <div class="zodiac-list"><strong>相冲：</strong>${shengxiaoAnalysis.incompatible.chong.join('、') || '无'}</div>
                            <div class="zodiac-list"><strong>相刑：</strong>${shengxiaoAnalysis.incompatible.xing.join('、') || '无'}</div>
                            <div class="zodiac-list"><strong>被刑：</strong>${shengxiaoAnalysis.incompatible.beixing.join('、') || '无'}</div>
                            <div class="zodiac-list"><strong>相害：</strong>${shengxiaoAnalysis.incompatible.hai.join('、') || '无'}</div>
                            <div class="zodiac-list"><strong>相破：</strong>${shengxiaoAnalysis.incompatible.po.join('、') || '无'}</div>
                        </div>
                    </div>
                    
                    <div style="margin-top: 20px; padding: 15px; background: #fff3cd; border-radius: 8px; border-left: 4px solid #ffc107;">
                        <p><strong>💡 说明：</strong>合生肖是合八字的一小部分，有一定参考意义，但不是全部。合婚请以八字为准。如果生肖同时在相合与不合中，则做加减即可。</p>
                    </div>
                </div>
                
                <div class="result-card">
                    <h3>八字详细分析</h3>
                    <div class="bazi-content">${formatBaziContent(baziAnalysis)}</div>
                </div>
            `;
            
            resultsDiv.style.display = 'block';
            
            // 显示命运轨迹按钮
            const destinyTrackBtn = document.getElementById('destinyTrackBtn');
            if (destinyTrackBtn) {
                destinyTrackBtn.style.display = 'inline-block';
            }
        }
        
        function displayError(message) {
            const resultsDiv = document.getElementById('results');
            const formattedMessage = message.replace(/\n/g, '<br>');
            resultsDiv.innerHTML = `<div class="error">❌ ${formattedMessage}</div>`;
            resultsDiv.style.display = 'block';
        }
        
        function getTimeLabel(hour) {
            const timeLabels = {
                '0': '子时 (23:00-01:00)',
                '1': '丑时 (01:00-03:00)',
                '3': '寅时 (03:00-05:00)',
                '5': '卯时 (05:00-07:00)',
                '7': '辰时 (07:00-09:00)',
                '8': '巳时 (09:00-11:00)',
                '11': '午时 (11:00-13:00)',
                '13': '未时 (13:00-15:00)',
                '15': '申时 (15:00-17:00)',
                '17': '酉时 (17:00-19:00)',
                '19': '戌时 (19:00-21:00)',
                '21': '亥时 (21:00-23:00)'
            };
            return timeLabels[hour] || `${hour}点`;
        }
        
        function cleanAnsiCodes(text) {
            // 清理ANSI颜色代码
            return text.replace(/\x1b\[[0-9;]*m/g, '');
        }
        
        function formatBaziContent(text) {
            if (!text) return '暂无分析结果';
            
            // 清理ANSI代码但保持原始格式
            let cleanText = cleanAnsiCodes(text);
            
            // 过滤推广信息
            let lines = cleanText.split('\n');
            let filteredLines = [];
            
            for (let line of lines) {
                // 过滤推广信息
                if (line.includes('建议参见') || line.includes('t.cn') || line.includes('http')) {
                    continue;
                }
                filteredLines.push(line);
            }
            
            return filteredLines.join('\n');
        }
        
        // 初始化日期选择器
        function initializeDateSelectors() {
            const currentYear = new Date().getFullYear();
            const yearSelect = document.getElementById('birthYear');
            const monthSelect = document.getElementById('birthMonth');
            const daySelect = document.getElementById('birthDay');
            
            // 生成年份选项 (1900-当前年份)
            for (let year = currentYear; year >= 1900; year--) {
                const option = document.createElement('option');
                option.value = year;
                option.textContent = year + '年';
                if (year === 1990) option.selected = true; // 默认选择1990年
                yearSelect.appendChild(option);
            }
            
            // 生成月份选项
            for (let month = 1; month <= 12; month++) {
                const option = document.createElement('option');
                option.value = month;
                option.textContent = month + '月';
                if (month === 1) option.selected = true; // 默认选择1月
                monthSelect.appendChild(option);
            }
            
            // 生成日期选项
            function updateDays() {
                const selectedYear = parseInt(yearSelect.value);
                const selectedMonth = parseInt(monthSelect.value);
                const daysInMonth = new Date(selectedYear, selectedMonth, 0).getDate();
                
                daySelect.innerHTML = '';
                for (let day = 1; day <= daysInMonth; day++) {
                    const option = document.createElement('option');
                    option.value = day;
                    option.textContent = day + '日';
                    if (day === 1) option.selected = true; // 默认选择1日
                    daySelect.appendChild(option);
                }
            }
            
            // 初始化日期
            updateDays();
            
            // 年份或月份改变时更新日期
            yearSelect.addEventListener('change', updateDays);
            monthSelect.addEventListener('change', updateDays);
        }
        
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', initializeDateSelectors);
        
        // 全局变量存储分析结果
        let currentAnalysisData = null;
        
        // AI配置和解读功能
        function toggleAIConfig() {
            const form = document.getElementById('aiConfigForm');
            form.classList.toggle('show');
            
            const button = event.target;
            button.textContent = form.classList.contains('show') ? 
                '⚙️ 隐藏AI设置' : '⚙️ 配置AI设置';
        }
        
        function updateAIConfig() {
            const provider = document.getElementById('aiProvider').value;
            const modelSelect = document.getElementById('aiModel');
            const apiUrlInput = document.getElementById('aiApiUrl');
            const customModelGroup = document.getElementById('customModelGroup');
            const customHeaderGroup = document.getElementById('customHeaderGroup');
            
            // 隐藏所有自定义字段
            customModelGroup.style.display = 'none';
            customHeaderGroup.style.display = 'none';
            
            // 根据服务商更新模型选项和API地址提示
            if (provider === 'openai') {
                modelSelect.innerHTML = `
                    <option value="gpt-3.5-turbo">GPT-3.5 Turbo</option>
                    <option value="gpt-4">GPT-4</option>
                    <option value="gpt-4-turbo">GPT-4 Turbo</option>
                    <option value="gpt-4o">GPT-4o</option>
                `;
                apiUrlInput.placeholder = "自定义API地址，留空使用默认 (https://api.openai.com/v1/chat/completions)";
                apiUrlInput.style.borderColor = "#ddd";
            } else if (provider === 'claude') {
                modelSelect.innerHTML = `
                    <option value="claude-3-sonnet-20240229">Claude 3 Sonnet</option>
                    <option value="claude-3-opus-20240229">Claude 3 Opus</option>
                    <option value="claude-3-haiku-20240307">Claude 3 Haiku</option>
                    <option value="claude-3-5-sonnet-20241022">Claude 3.5 Sonnet</option>
                `;
                apiUrlInput.placeholder = "自定义API地址，留空使用默认 (https://api.anthropic.com/v1/messages)";
                apiUrlInput.style.borderColor = "#ddd";
            } else if (provider === 'deepseek') {
                modelSelect.innerHTML = `
                    <option value="deepseek-chat">DeepSeek Chat</option>
                    <option value="deepseek-coder">DeepSeek Coder</option>
                `;
                apiUrlInput.placeholder = "自定义API地址，留空使用默认 (https://api.deepseek.com/v1/chat/completions)";
                apiUrlInput.style.borderColor = "#ddd";
            } else if (provider === 'custom') {
                modelSelect.innerHTML = `
                    <option value="gpt-3.5-turbo">GPT-3.5 Turbo (OpenAI兼容)</option>
                    <option value="gpt-4">GPT-4 (OpenAI兼容)</option>
                    <option value="claude-3-sonnet">Claude-3-Sonnet (Claude兼容)</option>
                    <option value="custom-model">使用下方自定义模型名</option>
                `;
                apiUrlInput.placeholder = "必填：自定义API地址 (如: https://your-api.com/v1/chat/completions)";
                apiUrlInput.style.borderColor = "#ff6b6b";
                
                // 显示自定义字段
                customModelGroup.style.display = 'block';
                customHeaderGroup.style.display = 'block';
            }
        }
        
        async function getAIInterpretation() {
            if (!currentAnalysisData) {
                alert('请先完成八字分析');
                return;
            }
            
            // 获取AI配置
            const provider = document.getElementById('aiProvider').value;
            const model = document.getElementById('aiModel').value;
            const apiKey = document.getElementById('aiApiKey').value;
            const apiUrl = document.getElementById('aiApiUrl').value;
            
            if (!apiKey.trim()) {
                alert('请输入API Key');
                return;
            }
            
            // 自定义服务商必须填写API地址
            if (provider === 'custom' && !apiUrl.trim()) {
                alert('自定义服务商必须填写API地址');
                return;
            }
            
            let finalModel = model;
            let aiConfig = {
                provider: provider,
                model: finalModel,
                api_key: apiKey.trim()
            };
            
            // 处理自定义配置
            if (provider === 'custom') {
                const customModel = document.getElementById('customModel').value;
                const customHeaders = document.getElementById('customHeaders').value;
                
                // 如果选择了自定义模型且填写了模型名，使用自定义模型名
                if (model === 'custom-model' && customModel.trim()) {
                    finalModel = customModel.trim();
                    aiConfig.model = finalModel;
                }
                
                // 自定义服务商必须有API地址
                aiConfig.api_url = apiUrl.trim();
                
                // 添加自定义headers
                if (customHeaders.trim()) {
                    try {
                        aiConfig.custom_headers = JSON.parse(customHeaders.trim());
                    } catch (e) {
                        alert('自定义Headers格式错误，请检查JSON格式');
                        return;
                    }
                }
            } else {
                // 其他服务商的API地址是可选的
                if (apiUrl.trim()) {
                    aiConfig.api_url = apiUrl.trim();
                }
            }
            
            // 显示加载状态和可折叠界面
            const resultDiv = document.getElementById('aiInterpretationResult');
            resultDiv.style.display = 'block';
            resultDiv.innerHTML = `
                <div class="ai-interpretation">
                    <div class="ai-interpretation-header">
                        <h3>🧠 AI智能解读</h3>
                        <button class="ai-collapse-btn" onclick="toggleAIResult()">▼</button>
                    </div>
                    <div class="ai-interpretation-content" id="aiContentArea">
                        <div class="ai-loading">🤖 AI正在分析中，请稍候...<span class="streaming-cursor">|</span></div>
                    </div>
                </div>
            `;
            
            const interpretBtn = document.getElementById('aiInterpretBtn');
            interpretBtn.disabled = true;
            interpretBtn.textContent = '🤖 AI分析中...';
            
            try {
                // 使用流式API端点
                const response = await fetch('/api/ai-interpretation-stream', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        birth_info: currentAnalysisData.birth_info,
                        shengxiao_analysis: currentAnalysisData.shengxiao_analysis,
                        bazi_analysis: currentAnalysisData.bazi_analysis,
                        ai_config: aiConfig
                    })
                });
                
                if (!response.ok) {
                    throw new Error(`HTTP错误: ${response.status}`);
                }
                
                const reader = response.body.getReader();
                const decoder = new TextDecoder();
                const contentArea = document.getElementById('aiContentArea');
                let fullContent = '';
                
                // 清空加载状态
                contentArea.innerHTML = '<div class="content"></div>';
                const contentDiv = contentArea.querySelector('.content');
                
                while (true) {
                    const { done, value } = await reader.read();
                    
                    if (done) {
                        break;
                    }
                    
                    const chunk = decoder.decode(value, { stream: true });
                    const lines = chunk.split('\n');
                    
                    for (const line of lines) {
                        if (line.startsWith('data: ')) {
                            const data = line.slice(6);
                            if (data.trim() === '') continue;
                            
                            try {
                                const parsed = JSON.parse(data);
                                
                                if (parsed.error) {
                                    contentDiv.innerHTML = `
                                        <div class="error">
                                            ❌ AI解读失败: ${parsed.error}
                                        </div>
                                    `;
                                    return;
                                }
                                
                                if (parsed.content) {
                                    fullContent += parsed.content;
                                    // 实时渲染Markdown内容
                                    contentDiv.innerHTML = marked.parse(fullContent) + '<span class="streaming-cursor">|</span>';
                                }
                                
                                if (parsed.done) {
                                    // 完成时移除光标并添加时间戳
                                    contentDiv.innerHTML = marked.parse(fullContent) + `
                                        <div style="text-align: right; margin-top: 15px; opacity: 0.8; font-size: 0.9em;">
                                            解读时间: ${new Date().toLocaleString()}
                                        </div>
                                    `;
                                    break;
                                }
                            } catch (e) {
                                console.error('解析JSON错误:', e);
                                continue;
                            }
                        }
                    }
                }
                
            } catch (error) {
                document.getElementById('aiContentArea').innerHTML = `
                    <div class="error">
                        ❌ 网络错误: ${error.message}
                    </div>
                `;
            } finally {
                interpretBtn.disabled = false;
                interpretBtn.textContent = '🧠 AI智能解读';
            }
        }
        
        // 切换AI结果显示/隐藏
        function toggleAIResult() {
            const content = document.querySelector('.ai-interpretation-content');
            const btn = document.querySelector('.ai-collapse-btn');
            
            if (content.style.display === 'none') {
                content.style.display = 'block';
                btn.textContent = '▼';
            } else {
                content.style.display = 'none';
                btn.textContent = '▶';
            }
        }
        
        // 打开命运轨迹页面
        function openDestinyTrack() {
            if (!currentAnalysisData) {
                alert('请先完成八字分析');
                return;
            }
            
            // 获取当前的AI配置
            const provider = document.getElementById('aiProvider').value;
            const model = document.getElementById('aiModel').value;
            const apiKey = document.getElementById('aiApiKey').value;
            const apiUrl = document.getElementById('aiApiUrl').value;
            const customModel = document.getElementById('customModel') ? document.getElementById('customModel').value : '';
            const customHeaders = document.getElementById('customHeaders') ? document.getElementById('customHeaders').value : '';
            
            let aiConfig = null;
            if (apiKey.trim()) {
                aiConfig = {
                    provider: provider,
                    model: model,
                    api_key: apiKey.trim()
                };
                
                if (provider === 'custom') {
                    if (model === 'custom-model' && customModel.trim()) {
                        aiConfig.model = customModel.trim();
                    }
                    if (apiUrl.trim()) {
                        aiConfig.api_url = apiUrl.trim();
                    }
                    if (customHeaders.trim()) {
                        try {
                            aiConfig.custom_headers = JSON.parse(customHeaders.trim());
                        } catch (e) {
                            // 忽略JSON解析错误
                        }
                    }
                } else if (apiUrl.trim()) {
                    aiConfig.api_url = apiUrl.trim();
                }
            }
            
            // 将八字数据和AI配置存储到localStorage
            localStorage.setItem('baziData', JSON.stringify(currentAnalysisData));
            if (aiConfig) {
                localStorage.setItem('destinyAiConfig', JSON.stringify(aiConfig));
            } else {
                localStorage.removeItem('destinyAiConfig');
            }
            
            // 跳转到命运轨迹页面
            window.open('/destiny-track', '_blank');
        }
        
        // 复制功能
        async function copyToClipboard(text, button) {
            try {
                await navigator.clipboard.writeText(text);
                const originalText = button.textContent;
                button.textContent = '✅ 已复制';
                button.classList.add('copy-success');
                setTimeout(() => {
                    button.textContent = originalText;
                    button.classList.remove('copy-success');
                }, 2000);
            } catch (err) {
                alert('复制失败，请手动复制');
            }
        }
        
        function copyShengxiaoInfo() {
            if (!currentAnalysisData) return;
            
            const birthInfo = currentAnalysisData.birth_info;
            const shengxiao = currentAnalysisData.shengxiao_analysis;
            
            const text = `生肖分析 - ${birthInfo.shengxiao}
            
基本信息：
出生日期：${birthInfo.date} (${birthInfo.calendar_type})
出生时辰：${getTimeLabel(birthInfo.time)}
性别：${birthInfo.gender}
生肖：${birthInfo.shengxiao}
年支：${shengxiao.year_zhi}

相合生肖：
三合：${shengxiao.compatible.sanhe.join('、') || '无'}
六合：${shengxiao.compatible.liuhe.join('、') || '无'}
三会：${shengxiao.compatible.sanhui.join('、') || '无'}

不合生肖：
相冲：${shengxiao.incompatible.chong.join('、') || '无'}
相刑：${shengxiao.incompatible.xing.join('、') || '无'}
被刑：${shengxiao.incompatible.beixing.join('、') || '无'}
相害：${shengxiao.incompatible.hai.join('、') || '无'}
相破：${shengxiao.incompatible.po.join('、') || '无'}

说明：合生肖是合八字的一小部分，有一定参考意义，但不是全部。合婚请以八字为准。`;
            
            copyToClipboard(text, event.target);
        }
        
        function copyBaziInfo() {
            if (!currentAnalysisData) return;
            
            const birthInfo = currentAnalysisData.birth_info;
            const baziText = formatBaziContent(currentAnalysisData.bazi_analysis);
            
            const text = `八字排盘
            
基本信息：
出生日期：${birthInfo.date} (${birthInfo.calendar_type})
出生时辰：${getTimeLabel(birthInfo.time)}
性别：${birthInfo.gender}
生肖：${birthInfo.shengxiao}

八字详细分析：
${baziText}`;
            
            copyToClipboard(text, event.target);
        }
        
        function copyAllInfo() {
            if (!currentAnalysisData) return;
            
            const birthInfo = currentAnalysisData.birth_info;
            const shengxiao = currentAnalysisData.shengxiao_analysis;
            const baziText = formatBaziContent(currentAnalysisData.bazi_analysis);
            
            const text = `八字生肖分析报告
            
基本信息：
出生日期：${birthInfo.date} (${birthInfo.calendar_type})
出生时辰：${getTimeLabel(birthInfo.time)}
性别：${birthInfo.gender}
生肖：${birthInfo.shengxiao}
年支：${shengxiao.year_zhi}

=== 生肖分析 ===
相合生肖：
三合：${shengxiao.compatible.sanhe.join('、') || '无'}
六合：${shengxiao.compatible.liuhe.join('、') || '无'}
三会：${shengxiao.compatible.sanhui.join('、') || '无'}

不合生肖：
相冲：${shengxiao.incompatible.chong.join('、') || '无'}
相刑：${shengxiao.incompatible.xing.join('、') || '无'}
被刑：${shengxiao.incompatible.beixing.join('、') || '无'}
相害：${shengxiao.incompatible.hai.join('、') || '无'}
相破：${shengxiao.incompatible.po.join('、') || '无'}

=== 八字详细分析 ===
${baziText}

说明：合生肖是合八字的一小部分，有一定参考意义，但不是全部。合婚请以八字为准。`;
            
            copyToClipboard(text, event.target);
        }
    </script>
</body>
</html>